package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;
import org.joda.time.DateTime;

@Entity
public class TestBorrowerWorkflowData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private DateTime createdAt;

  @Value(
      optional = true,
      nullable = true
  )
  private String activeCreditPullId;

  public TestBorrowerWorkflowData() {
    // JSON
  }

  public TestBorrowerWorkflowData(TestEntityAction action, String id, DateTime createdAt,
      String activeCreditPullId) {
    this.action = action;
    this.id = id;
    this.createdAt = createdAt;
    this.activeCreditPullId = activeCreditPullId;
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<DateTime> getCreatedAt() {
    return Option.of(createdAt);
  }

  public Option<String> getActiveCreditPullId() {
    return Option.of(activeCreditPullId);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.createdAt, this.activeCreditPullId);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestBorrowerWorkflowData that = (TestBorrowerWorkflowData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(createdAt, that.createdAt) &&
        Objects.equals(activeCreditPullId, that.activeCreditPullId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestBorrowerWorkflowData.class);
    if (isExactClass) {
      sb.append("TestBorrowerWorkflowData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  createdAt: ").append(createdAt).append("\n");
    sb.append("  activeCreditPullId: ").append(activeCreditPullId).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withCreatedAt(getCreatedAt().getOrNull())
      .withActiveCreditPullId(getActiveCreditPullId().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private DateTime createdAt = null;

    @Nullable
    private String activeCreditPullId = null;

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withCreatedAt(@Nullable DateTime createdAt) {
      this.createdAt = createdAt;
      return this;
    }

    public Builder withActiveCreditPullId(@Nullable String activeCreditPullId) {
      this.activeCreditPullId = activeCreditPullId;
      return this;
    }

    public TestBorrowerWorkflowData build() {
      TestBorrowerWorkflowData obj1 = new TestBorrowerWorkflowData(action, id, createdAt, activeCreditPullId);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestBorrowerWorkflowData buildForTesting() {
      TestBorrowerWorkflowData obj1 = new TestBorrowerWorkflowData(action, id, createdAt, activeCreditPullId);
      return obj1;
    }
  }
}
