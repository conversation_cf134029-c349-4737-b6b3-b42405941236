package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.entities.EmailAddress;
import com.kaching.entities.PhoneNumber;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

@Entity
public class TestBorrowerData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private TestIncomeWorkflowData incomeWorkflowData;

  @Value(
      optional = true,
      nullable = true
  )
  private TestBorrowerType borrowerType;

  @Value(
      optional = true,
      nullable = true
  )
  private String firstName;

  @Value(
      optional = true,
      nullable = true
  )
  private String middleName;

  @Value(
      optional = true,
      nullable = true
  )
  private String lastName;

  @Value(
      optional = true,
      nullable = true
  )
  private String suffix;

  @Value(
      optional = true,
      nullable = true
  )
  private List<String> alternativeNames;

  @Value(
      optional = true,
      nullable = true
  )
  private DateTime termsOfServiceConsentedAt;

  @Value(
      optional = true,
      nullable = true
  )
  private DateTime creditPullConsentedAt;

  @Value(
      optional = true,
      nullable = true
  )
  private EmailAddress emailAddress;

  @Value(
      optional = true,
      nullable = true
  )
  private PhoneNumber phoneNumber;

  @Value(
      optional = true,
      nullable = true
  )
  private LocalDate dateOfBirth;

  @Value(
      optional = true,
      nullable = true
  )
  private TestAddressData currentAddress;

  @Value(
      optional = true,
      nullable = true
  )
  private TestAddressData mailingAddress;

  @Value(
      optional = true,
      nullable = true
  )
  private TestMaritalStatus maritalStatus;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCitizenshipType citizenshipType;

  @Value(
      optional = true,
      nullable = true
  )
  private String spouseBorrowerId;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean isCurrentlyLivingWithCoBorrowers;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean isIntendingToOccupy;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean isPrimaryAuthorized;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean isSharingInformationWithCoBorrowers;

  @Value(
      optional = true,
      nullable = true
  )
  private TestMilitaryStatus militaryStatus;

  @Value(
      optional = true,
      nullable = true
  )
  private LocalDate militaryServiceExpectedCompletionDate;

  @Value(
      optional = true,
      nullable = true
  )
  private TestEmploymentStatus employmentStatus;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullConsentType softCreditPullConsentType;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullStatus creditPullStatus;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullType creditPullType;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestCreditScoreData> creditScoreData;

  @Value(
      optional = true,
      nullable = true
  )
  private TestBorrowerWorkflowData borrowerWorkflowData;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean homeownerPastThreeYears;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean outstandingJudgmentsIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean presentlyDelinquentIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean partyToLawsuitIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean priorPropertyDeedInLieuConveyedIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean priorPropertyShortSaleCompletedIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean priorPropertyForeclosureCompletedIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean bankruptcyIndicator;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestBankruptcyData> bankruptcies;

  public TestBorrowerData() {
    // JSON
  }

  public TestBorrowerData(TestEntityAction action, String id,
      TestIncomeWorkflowData incomeWorkflowData, TestBorrowerType borrowerType, String firstName,
      String middleName, String lastName, String suffix, List<String> alternativeNames,
      DateTime termsOfServiceConsentedAt, DateTime creditPullConsentedAt, EmailAddress emailAddress,
      PhoneNumber phoneNumber, LocalDate dateOfBirth, TestAddressData currentAddress,
      TestAddressData mailingAddress, TestMaritalStatus maritalStatus,
      TestCitizenshipType citizenshipType, String spouseBorrowerId,
      Boolean isCurrentlyLivingWithCoBorrowers, Boolean isIntendingToOccupy,
      Boolean isPrimaryAuthorized, Boolean isSharingInformationWithCoBorrowers,
      TestMilitaryStatus militaryStatus, LocalDate militaryServiceExpectedCompletionDate,
      TestEmploymentStatus employmentStatus, TestCreditPullConsentType softCreditPullConsentType,
      TestCreditPullStatus creditPullStatus, TestCreditPullType creditPullType,
      List<TestCreditScoreData> creditScoreData, TestBorrowerWorkflowData borrowerWorkflowData,
      Boolean homeownerPastThreeYears, Boolean outstandingJudgmentsIndicator,
      Boolean presentlyDelinquentIndicator, Boolean partyToLawsuitIndicator,
      Boolean priorPropertyDeedInLieuConveyedIndicator,
      Boolean priorPropertyShortSaleCompletedIndicator,
      Boolean priorPropertyForeclosureCompletedIndicator, Boolean bankruptcyIndicator,
      List<TestBankruptcyData> bankruptcies) {
    this.action = action;
    this.id = id;
    this.incomeWorkflowData = incomeWorkflowData;
    this.borrowerType = borrowerType;
    this.firstName = firstName;
    this.middleName = middleName;
    this.lastName = lastName;
    this.suffix = suffix;
    this.alternativeNames = alternativeNames;
    this.termsOfServiceConsentedAt = termsOfServiceConsentedAt;
    this.creditPullConsentedAt = creditPullConsentedAt;
    this.emailAddress = emailAddress;
    this.phoneNumber = phoneNumber;
    this.dateOfBirth = dateOfBirth;
    this.currentAddress = currentAddress;
    this.mailingAddress = mailingAddress;
    this.maritalStatus = maritalStatus;
    this.citizenshipType = citizenshipType;
    this.spouseBorrowerId = spouseBorrowerId;
    this.isCurrentlyLivingWithCoBorrowers = isCurrentlyLivingWithCoBorrowers;
    this.isIntendingToOccupy = isIntendingToOccupy;
    this.isPrimaryAuthorized = isPrimaryAuthorized;
    this.isSharingInformationWithCoBorrowers = isSharingInformationWithCoBorrowers;
    this.militaryStatus = militaryStatus;
    this.militaryServiceExpectedCompletionDate = militaryServiceExpectedCompletionDate;
    this.employmentStatus = employmentStatus;
    this.softCreditPullConsentType = softCreditPullConsentType;
    this.creditPullStatus = creditPullStatus;
    this.creditPullType = creditPullType;
    this.creditScoreData = creditScoreData;
    this.borrowerWorkflowData = borrowerWorkflowData;
    this.homeownerPastThreeYears = homeownerPastThreeYears;
    this.outstandingJudgmentsIndicator = outstandingJudgmentsIndicator;
    this.presentlyDelinquentIndicator = presentlyDelinquentIndicator;
    this.partyToLawsuitIndicator = partyToLawsuitIndicator;
    this.priorPropertyDeedInLieuConveyedIndicator = priorPropertyDeedInLieuConveyedIndicator;
    this.priorPropertyShortSaleCompletedIndicator = priorPropertyShortSaleCompletedIndicator;
    this.priorPropertyForeclosureCompletedIndicator = priorPropertyForeclosureCompletedIndicator;
    this.bankruptcyIndicator = bankruptcyIndicator;
    this.bankruptcies = bankruptcies;
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<TestIncomeWorkflowData> getIncomeWorkflowData() {
    return Option.of(incomeWorkflowData);
  }

  public Option<TestBorrowerType> getBorrowerType() {
    return Option.of(borrowerType);
  }

  public Option<String> getFirstName() {
    return Option.of(firstName);
  }

  public Option<String> getMiddleName() {
    return Option.of(middleName);
  }

  public Option<String> getLastName() {
    return Option.of(lastName);
  }

  public Option<String> getSuffix() {
    return Option.of(suffix);
  }

  public Option<List<String>> getAlternativeNames() {
    return Option.of(alternativeNames);
  }

  public Option<DateTime> getTermsOfServiceConsentedAt() {
    return Option.of(termsOfServiceConsentedAt);
  }

  public Option<DateTime> getCreditPullConsentedAt() {
    return Option.of(creditPullConsentedAt);
  }

  public Option<EmailAddress> getEmailAddress() {
    return Option.of(emailAddress);
  }

  public Option<PhoneNumber> getPhoneNumber() {
    return Option.of(phoneNumber);
  }

  public Option<LocalDate> getDateOfBirth() {
    return Option.of(dateOfBirth);
  }

  public Option<TestAddressData> getCurrentAddress() {
    return Option.of(currentAddress);
  }

  public Option<TestAddressData> getMailingAddress() {
    return Option.of(mailingAddress);
  }

  public Option<TestMaritalStatus> getMaritalStatus() {
    return Option.of(maritalStatus);
  }

  public Option<TestCitizenshipType> getCitizenshipType() {
    return Option.of(citizenshipType);
  }

  public Option<String> getSpouseBorrowerId() {
    return Option.of(spouseBorrowerId);
  }

  public Option<Boolean> getIsCurrentlyLivingWithCoBorrowers() {
    return Option.of(isCurrentlyLivingWithCoBorrowers);
  }

  public Option<Boolean> getIsIntendingToOccupy() {
    return Option.of(isIntendingToOccupy);
  }

  public Option<Boolean> getIsPrimaryAuthorized() {
    return Option.of(isPrimaryAuthorized);
  }

  public Option<Boolean> getIsSharingInformationWithCoBorrowers() {
    return Option.of(isSharingInformationWithCoBorrowers);
  }

  public Option<TestMilitaryStatus> getMilitaryStatus() {
    return Option.of(militaryStatus);
  }

  public Option<LocalDate> getMilitaryServiceExpectedCompletionDate() {
    return Option.of(militaryServiceExpectedCompletionDate);
  }

  public Option<TestEmploymentStatus> getEmploymentStatus() {
    return Option.of(employmentStatus);
  }

  public Option<TestCreditPullConsentType> getSoftCreditPullConsentType() {
    return Option.of(softCreditPullConsentType);
  }

  public Option<TestCreditPullStatus> getCreditPullStatus() {
    return Option.of(creditPullStatus);
  }

  public Option<TestCreditPullType> getCreditPullType() {
    return Option.of(creditPullType);
  }

  public Option<List<TestCreditScoreData>> getCreditScoreData() {
    return Option.of(creditScoreData);
  }

  public Option<TestBorrowerWorkflowData> getBorrowerWorkflowData() {
    return Option.of(borrowerWorkflowData);
  }

  public Option<Boolean> getHomeownerPastThreeYears() {
    return Option.of(homeownerPastThreeYears);
  }

  public Option<Boolean> getOutstandingJudgmentsIndicator() {
    return Option.of(outstandingJudgmentsIndicator);
  }

  public Option<Boolean> getPresentlyDelinquentIndicator() {
    return Option.of(presentlyDelinquentIndicator);
  }

  public Option<Boolean> getPartyToLawsuitIndicator() {
    return Option.of(partyToLawsuitIndicator);
  }

  public Option<Boolean> getPriorPropertyDeedInLieuConveyedIndicator() {
    return Option.of(priorPropertyDeedInLieuConveyedIndicator);
  }

  public Option<Boolean> getPriorPropertyShortSaleCompletedIndicator() {
    return Option.of(priorPropertyShortSaleCompletedIndicator);
  }

  public Option<Boolean> getPriorPropertyForeclosureCompletedIndicator() {
    return Option.of(priorPropertyForeclosureCompletedIndicator);
  }

  public Option<Boolean> getBankruptcyIndicator() {
    return Option.of(bankruptcyIndicator);
  }

  public Option<List<TestBankruptcyData>> getBankruptcies() {
    return Option.of(bankruptcies);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.incomeWorkflowData, this.borrowerType, this.firstName, this.middleName, this.lastName, this.suffix, this.alternativeNames, this.termsOfServiceConsentedAt, this.creditPullConsentedAt, this.emailAddress, this.phoneNumber, this.dateOfBirth, this.currentAddress, this.mailingAddress, this.maritalStatus, this.citizenshipType, this.spouseBorrowerId, this.isCurrentlyLivingWithCoBorrowers, this.isIntendingToOccupy, this.isPrimaryAuthorized, this.isSharingInformationWithCoBorrowers, this.militaryStatus, this.militaryServiceExpectedCompletionDate, this.employmentStatus, this.softCreditPullConsentType, this.creditPullStatus, this.creditPullType, this.creditScoreData, this.borrowerWorkflowData, this.homeownerPastThreeYears, this.outstandingJudgmentsIndicator, this.presentlyDelinquentIndicator, this.partyToLawsuitIndicator, this.priorPropertyDeedInLieuConveyedIndicator, this.priorPropertyShortSaleCompletedIndicator, this.priorPropertyForeclosureCompletedIndicator, this.bankruptcyIndicator, this.bankruptcies);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestBorrowerData that = (TestBorrowerData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(incomeWorkflowData, that.incomeWorkflowData) &&
        Objects.equals(borrowerType, that.borrowerType) &&
        Objects.equals(firstName, that.firstName) &&
        Objects.equals(middleName, that.middleName) &&
        Objects.equals(lastName, that.lastName) &&
        Objects.equals(suffix, that.suffix) &&
        Objects.equals(alternativeNames, that.alternativeNames) &&
        Objects.equals(termsOfServiceConsentedAt, that.termsOfServiceConsentedAt) &&
        Objects.equals(creditPullConsentedAt, that.creditPullConsentedAt) &&
        Objects.equals(emailAddress, that.emailAddress) &&
        Objects.equals(phoneNumber, that.phoneNumber) &&
        Objects.equals(dateOfBirth, that.dateOfBirth) &&
        Objects.equals(currentAddress, that.currentAddress) &&
        Objects.equals(mailingAddress, that.mailingAddress) &&
        Objects.equals(maritalStatus, that.maritalStatus) &&
        Objects.equals(citizenshipType, that.citizenshipType) &&
        Objects.equals(spouseBorrowerId, that.spouseBorrowerId) &&
        Objects.equals(isCurrentlyLivingWithCoBorrowers, that.isCurrentlyLivingWithCoBorrowers) &&
        Objects.equals(isIntendingToOccupy, that.isIntendingToOccupy) &&
        Objects.equals(isPrimaryAuthorized, that.isPrimaryAuthorized) &&
        Objects.equals(isSharingInformationWithCoBorrowers, that.isSharingInformationWithCoBorrowers) &&
        Objects.equals(militaryStatus, that.militaryStatus) &&
        Objects.equals(militaryServiceExpectedCompletionDate, that.militaryServiceExpectedCompletionDate) &&
        Objects.equals(employmentStatus, that.employmentStatus) &&
        Objects.equals(softCreditPullConsentType, that.softCreditPullConsentType) &&
        Objects.equals(creditPullStatus, that.creditPullStatus) &&
        Objects.equals(creditPullType, that.creditPullType) &&
        Objects.equals(creditScoreData, that.creditScoreData) &&
        Objects.equals(borrowerWorkflowData, that.borrowerWorkflowData) &&
        Objects.equals(homeownerPastThreeYears, that.homeownerPastThreeYears) &&
        Objects.equals(outstandingJudgmentsIndicator, that.outstandingJudgmentsIndicator) &&
        Objects.equals(presentlyDelinquentIndicator, that.presentlyDelinquentIndicator) &&
        Objects.equals(partyToLawsuitIndicator, that.partyToLawsuitIndicator) &&
        Objects.equals(priorPropertyDeedInLieuConveyedIndicator, that.priorPropertyDeedInLieuConveyedIndicator) &&
        Objects.equals(priorPropertyShortSaleCompletedIndicator, that.priorPropertyShortSaleCompletedIndicator) &&
        Objects.equals(priorPropertyForeclosureCompletedIndicator, that.priorPropertyForeclosureCompletedIndicator) &&
        Objects.equals(bankruptcyIndicator, that.bankruptcyIndicator) &&
        Objects.equals(bankruptcies, that.bankruptcies);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestBorrowerData.class);
    if (isExactClass) {
      sb.append("TestBorrowerData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  incomeWorkflowData: ").append(incomeWorkflowData == null ? "null" : incomeWorkflowData.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  borrowerType: ").append(borrowerType).append("\n");
    sb.append("  firstName: ").append(firstName).append("\n");
    sb.append("  middleName: ").append(middleName).append("\n");
    sb.append("  lastName: ").append(lastName).append("\n");
    sb.append("  suffix: ").append(suffix).append("\n");
    sb.append("  alternativeNames: ").append(alternativeNames == null ? "null" : alternativeNames.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  termsOfServiceConsentedAt: ").append(termsOfServiceConsentedAt).append("\n");
    sb.append("  creditPullConsentedAt: ").append(creditPullConsentedAt).append("\n");
    sb.append("  emailAddress: ").append(emailAddress).append("\n");
    sb.append("  phoneNumber: ").append(phoneNumber).append("\n");
    sb.append("  dateOfBirth: ").append(dateOfBirth).append("\n");
    sb.append("  currentAddress: ").append(currentAddress == null ? "null" : currentAddress.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  mailingAddress: ").append(mailingAddress == null ? "null" : mailingAddress.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  maritalStatus: ").append(maritalStatus).append("\n");
    sb.append("  citizenshipType: ").append(citizenshipType).append("\n");
    sb.append("  spouseBorrowerId: ").append(spouseBorrowerId).append("\n");
    sb.append("  isCurrentlyLivingWithCoBorrowers: ").append(isCurrentlyLivingWithCoBorrowers).append("\n");
    sb.append("  isIntendingToOccupy: ").append(isIntendingToOccupy).append("\n");
    sb.append("  isPrimaryAuthorized: ").append(isPrimaryAuthorized).append("\n");
    sb.append("  isSharingInformationWithCoBorrowers: ").append(isSharingInformationWithCoBorrowers).append("\n");
    sb.append("  militaryStatus: ").append(militaryStatus).append("\n");
    sb.append("  militaryServiceExpectedCompletionDate: ").append(militaryServiceExpectedCompletionDate).append("\n");
    sb.append("  employmentStatus: ").append(employmentStatus).append("\n");
    sb.append("  softCreditPullConsentType: ").append(softCreditPullConsentType).append("\n");
    sb.append("  creditPullStatus: ").append(creditPullStatus).append("\n");
    sb.append("  creditPullType: ").append(creditPullType).append("\n");
    sb.append("  creditScoreData: ").append(creditScoreData == null ? "null" : creditScoreData.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  borrowerWorkflowData: ").append(borrowerWorkflowData == null ? "null" : borrowerWorkflowData.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  homeownerPastThreeYears: ").append(homeownerPastThreeYears).append("\n");
    sb.append("  outstandingJudgmentsIndicator: ").append(outstandingJudgmentsIndicator).append("\n");
    sb.append("  presentlyDelinquentIndicator: ").append(presentlyDelinquentIndicator).append("\n");
    sb.append("  partyToLawsuitIndicator: ").append(partyToLawsuitIndicator).append("\n");
    sb.append("  priorPropertyDeedInLieuConveyedIndicator: ").append(priorPropertyDeedInLieuConveyedIndicator).append("\n");
    sb.append("  priorPropertyShortSaleCompletedIndicator: ").append(priorPropertyShortSaleCompletedIndicator).append("\n");
    sb.append("  priorPropertyForeclosureCompletedIndicator: ").append(priorPropertyForeclosureCompletedIndicator).append("\n");
    sb.append("  bankruptcyIndicator: ").append(bankruptcyIndicator).append("\n");
    sb.append("  bankruptcies: ").append(bankruptcies == null ? "null" : bankruptcies.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withIncomeWorkflowData(getIncomeWorkflowData().getOrNull())
      .withBorrowerType(getBorrowerType().getOrNull())
      .withFirstName(getFirstName().getOrNull())
      .withMiddleName(getMiddleName().getOrNull())
      .withLastName(getLastName().getOrNull())
      .withSuffix(getSuffix().getOrNull())
      .withAlternativeNames(getAlternativeNames().getOrNull())
      .withTermsOfServiceConsentedAt(getTermsOfServiceConsentedAt().getOrNull())
      .withCreditPullConsentedAt(getCreditPullConsentedAt().getOrNull())
      .withEmailAddress(getEmailAddress().getOrNull())
      .withPhoneNumber(getPhoneNumber().getOrNull())
      .withDateOfBirth(getDateOfBirth().getOrNull())
      .withCurrentAddress(getCurrentAddress().getOrNull())
      .withMailingAddress(getMailingAddress().getOrNull())
      .withMaritalStatus(getMaritalStatus().getOrNull())
      .withCitizenshipType(getCitizenshipType().getOrNull())
      .withSpouseBorrowerId(getSpouseBorrowerId().getOrNull())
      .withIsCurrentlyLivingWithCoBorrowers(getIsCurrentlyLivingWithCoBorrowers().getOrNull())
      .withIsIntendingToOccupy(getIsIntendingToOccupy().getOrNull())
      .withIsPrimaryAuthorized(getIsPrimaryAuthorized().getOrNull())
      .withIsSharingInformationWithCoBorrowers(getIsSharingInformationWithCoBorrowers().getOrNull())
      .withMilitaryStatus(getMilitaryStatus().getOrNull())
      .withMilitaryServiceExpectedCompletionDate(getMilitaryServiceExpectedCompletionDate().getOrNull())
      .withEmploymentStatus(getEmploymentStatus().getOrNull())
      .withSoftCreditPullConsentType(getSoftCreditPullConsentType().getOrNull())
      .withCreditPullStatus(getCreditPullStatus().getOrNull())
      .withCreditPullType(getCreditPullType().getOrNull())
      .withCreditScoreData(getCreditScoreData().getOrNull())
      .withBorrowerWorkflowData(getBorrowerWorkflowData().getOrNull())
      .withHomeownerPastThreeYears(getHomeownerPastThreeYears().getOrNull())
      .withOutstandingJudgmentsIndicator(getOutstandingJudgmentsIndicator().getOrNull())
      .withPresentlyDelinquentIndicator(getPresentlyDelinquentIndicator().getOrNull())
      .withPartyToLawsuitIndicator(getPartyToLawsuitIndicator().getOrNull())
      .withPriorPropertyDeedInLieuConveyedIndicator(getPriorPropertyDeedInLieuConveyedIndicator().getOrNull())
      .withPriorPropertyShortSaleCompletedIndicator(getPriorPropertyShortSaleCompletedIndicator().getOrNull())
      .withPriorPropertyForeclosureCompletedIndicator(getPriorPropertyForeclosureCompletedIndicator().getOrNull())
      .withBankruptcyIndicator(getBankruptcyIndicator().getOrNull())
      .withBankruptcies(getBankruptcies().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private TestIncomeWorkflowData incomeWorkflowData = null;

    @Nullable
    private TestBorrowerType borrowerType = null;

    @Nullable
    private String firstName = null;

    @Nullable
    private String middleName = null;

    @Nullable
    private String lastName = null;

    @Nullable
    private String suffix = null;

    @Nullable
    private List<String> alternativeNames = new ArrayList<>();

    @Nullable
    private DateTime termsOfServiceConsentedAt = null;

    @Nullable
    private DateTime creditPullConsentedAt = null;

    @Nullable
    private EmailAddress emailAddress = null;

    @Nullable
    private PhoneNumber phoneNumber = null;

    @Nullable
    private LocalDate dateOfBirth = null;

    @Nullable
    private TestAddressData currentAddress = null;

    @Nullable
    private TestAddressData mailingAddress = null;

    @Nullable
    private TestMaritalStatus maritalStatus = null;

    @Nullable
    private TestCitizenshipType citizenshipType = null;

    @Nullable
    private String spouseBorrowerId = null;

    @Nullable
    private Boolean isCurrentlyLivingWithCoBorrowers = null;

    @Nullable
    private Boolean isIntendingToOccupy = null;

    @Nullable
    private Boolean isPrimaryAuthorized = null;

    @Nullable
    private Boolean isSharingInformationWithCoBorrowers = null;

    @Nullable
    private TestMilitaryStatus militaryStatus = null;

    @Nullable
    private LocalDate militaryServiceExpectedCompletionDate = null;

    @Nullable
    private TestEmploymentStatus employmentStatus = null;

    @Nullable
    private TestCreditPullConsentType softCreditPullConsentType = null;

    @Nullable
    private TestCreditPullStatus creditPullStatus = null;

    @Nullable
    private TestCreditPullType creditPullType = null;

    @Nullable
    private List<TestCreditScoreData> creditScoreData = new ArrayList<>();

    @Nullable
    private TestBorrowerWorkflowData borrowerWorkflowData = null;

    @Nullable
    private Boolean homeownerPastThreeYears = null;

    @Nullable
    private Boolean outstandingJudgmentsIndicator = null;

    @Nullable
    private Boolean presentlyDelinquentIndicator = null;

    @Nullable
    private Boolean partyToLawsuitIndicator = null;

    @Nullable
    private Boolean priorPropertyDeedInLieuConveyedIndicator = null;

    @Nullable
    private Boolean priorPropertyShortSaleCompletedIndicator = null;

    @Nullable
    private Boolean priorPropertyForeclosureCompletedIndicator = null;

    @Nullable
    private Boolean bankruptcyIndicator = null;

    @Nullable
    private List<TestBankruptcyData> bankruptcies = new ArrayList<>();

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withIncomeWorkflowData(@Nullable TestIncomeWorkflowData incomeWorkflowData) {
      this.incomeWorkflowData = incomeWorkflowData;
      return this;
    }

    public Builder withBorrowerType(@Nullable TestBorrowerType borrowerType) {
      this.borrowerType = borrowerType;
      return this;
    }

    public Builder withFirstName(@Nullable String firstName) {
      this.firstName = firstName;
      return this;
    }

    public Builder withMiddleName(@Nullable String middleName) {
      this.middleName = middleName;
      return this;
    }

    public Builder withLastName(@Nullable String lastName) {
      this.lastName = lastName;
      return this;
    }

    public Builder withSuffix(@Nullable String suffix) {
      this.suffix = suffix;
      return this;
    }

    public Builder withAlternativeNames(@Nullable List<String> alternativeNames) {
      this.alternativeNames = alternativeNames;
      return this;
    }

    public Builder withTermsOfServiceConsentedAt(@Nullable DateTime termsOfServiceConsentedAt) {
      this.termsOfServiceConsentedAt = termsOfServiceConsentedAt;
      return this;
    }

    public Builder withCreditPullConsentedAt(@Nullable DateTime creditPullConsentedAt) {
      this.creditPullConsentedAt = creditPullConsentedAt;
      return this;
    }

    public Builder withEmailAddress(@Nullable EmailAddress emailAddress) {
      this.emailAddress = emailAddress;
      return this;
    }

    public Builder withPhoneNumber(@Nullable PhoneNumber phoneNumber) {
      this.phoneNumber = phoneNumber;
      return this;
    }

    public Builder withDateOfBirth(@Nullable LocalDate dateOfBirth) {
      this.dateOfBirth = dateOfBirth;
      return this;
    }

    public Builder withCurrentAddress(@Nullable TestAddressData currentAddress) {
      this.currentAddress = currentAddress;
      return this;
    }

    public Builder withMailingAddress(@Nullable TestAddressData mailingAddress) {
      this.mailingAddress = mailingAddress;
      return this;
    }

    public Builder withMaritalStatus(@Nullable TestMaritalStatus maritalStatus) {
      this.maritalStatus = maritalStatus;
      return this;
    }

    public Builder withCitizenshipType(@Nullable TestCitizenshipType citizenshipType) {
      this.citizenshipType = citizenshipType;
      return this;
    }

    public Builder withSpouseBorrowerId(@Nullable String spouseBorrowerId) {
      this.spouseBorrowerId = spouseBorrowerId;
      return this;
    }

    public Builder withIsCurrentlyLivingWithCoBorrowers(
        @Nullable Boolean isCurrentlyLivingWithCoBorrowers) {
      this.isCurrentlyLivingWithCoBorrowers = isCurrentlyLivingWithCoBorrowers;
      return this;
    }

    public Builder withIsIntendingToOccupy(@Nullable Boolean isIntendingToOccupy) {
      this.isIntendingToOccupy = isIntendingToOccupy;
      return this;
    }

    public Builder withIsPrimaryAuthorized(@Nullable Boolean isPrimaryAuthorized) {
      this.isPrimaryAuthorized = isPrimaryAuthorized;
      return this;
    }

    public Builder withIsSharingInformationWithCoBorrowers(
        @Nullable Boolean isSharingInformationWithCoBorrowers) {
      this.isSharingInformationWithCoBorrowers = isSharingInformationWithCoBorrowers;
      return this;
    }

    public Builder withMilitaryStatus(@Nullable TestMilitaryStatus militaryStatus) {
      this.militaryStatus = militaryStatus;
      return this;
    }

    public Builder withMilitaryServiceExpectedCompletionDate(
        @Nullable LocalDate militaryServiceExpectedCompletionDate) {
      this.militaryServiceExpectedCompletionDate = militaryServiceExpectedCompletionDate;
      return this;
    }

    public Builder withEmploymentStatus(@Nullable TestEmploymentStatus employmentStatus) {
      this.employmentStatus = employmentStatus;
      return this;
    }

    public Builder withSoftCreditPullConsentType(
        @Nullable TestCreditPullConsentType softCreditPullConsentType) {
      this.softCreditPullConsentType = softCreditPullConsentType;
      return this;
    }

    public Builder withCreditPullStatus(@Nullable TestCreditPullStatus creditPullStatus) {
      this.creditPullStatus = creditPullStatus;
      return this;
    }

    public Builder withCreditPullType(@Nullable TestCreditPullType creditPullType) {
      this.creditPullType = creditPullType;
      return this;
    }

    public Builder withCreditScoreData(@Nullable List<TestCreditScoreData> creditScoreData) {
      this.creditScoreData = creditScoreData;
      return this;
    }

    public Builder withBorrowerWorkflowData(
        @Nullable TestBorrowerWorkflowData borrowerWorkflowData) {
      this.borrowerWorkflowData = borrowerWorkflowData;
      return this;
    }

    public Builder withHomeownerPastThreeYears(@Nullable Boolean homeownerPastThreeYears) {
      this.homeownerPastThreeYears = homeownerPastThreeYears;
      return this;
    }

    public Builder withOutstandingJudgmentsIndicator(
        @Nullable Boolean outstandingJudgmentsIndicator) {
      this.outstandingJudgmentsIndicator = outstandingJudgmentsIndicator;
      return this;
    }

    public Builder withPresentlyDelinquentIndicator(
        @Nullable Boolean presentlyDelinquentIndicator) {
      this.presentlyDelinquentIndicator = presentlyDelinquentIndicator;
      return this;
    }

    public Builder withPartyToLawsuitIndicator(@Nullable Boolean partyToLawsuitIndicator) {
      this.partyToLawsuitIndicator = partyToLawsuitIndicator;
      return this;
    }

    public Builder withPriorPropertyDeedInLieuConveyedIndicator(
        @Nullable Boolean priorPropertyDeedInLieuConveyedIndicator) {
      this.priorPropertyDeedInLieuConveyedIndicator = priorPropertyDeedInLieuConveyedIndicator;
      return this;
    }

    public Builder withPriorPropertyShortSaleCompletedIndicator(
        @Nullable Boolean priorPropertyShortSaleCompletedIndicator) {
      this.priorPropertyShortSaleCompletedIndicator = priorPropertyShortSaleCompletedIndicator;
      return this;
    }

    public Builder withPriorPropertyForeclosureCompletedIndicator(
        @Nullable Boolean priorPropertyForeclosureCompletedIndicator) {
      this.priorPropertyForeclosureCompletedIndicator = priorPropertyForeclosureCompletedIndicator;
      return this;
    }

    public Builder withBankruptcyIndicator(@Nullable Boolean bankruptcyIndicator) {
      this.bankruptcyIndicator = bankruptcyIndicator;
      return this;
    }

    public Builder withBankruptcies(@Nullable List<TestBankruptcyData> bankruptcies) {
      this.bankruptcies = bankruptcies;
      return this;
    }

    public TestBorrowerData build() {
      TestBorrowerData obj1 = new TestBorrowerData(action, id, incomeWorkflowData, borrowerType, firstName, middleName, lastName, suffix, alternativeNames, termsOfServiceConsentedAt, creditPullConsentedAt, emailAddress, phoneNumber, dateOfBirth, currentAddress, mailingAddress, maritalStatus, citizenshipType, spouseBorrowerId, isCurrentlyLivingWithCoBorrowers, isIntendingToOccupy, isPrimaryAuthorized, isSharingInformationWithCoBorrowers, militaryStatus, militaryServiceExpectedCompletionDate, employmentStatus, softCreditPullConsentType, creditPullStatus, creditPullType, creditScoreData, borrowerWorkflowData, homeownerPastThreeYears, outstandingJudgmentsIndicator, presentlyDelinquentIndicator, partyToLawsuitIndicator, priorPropertyDeedInLieuConveyedIndicator, priorPropertyShortSaleCompletedIndicator, priorPropertyForeclosureCompletedIndicator, bankruptcyIndicator, bankruptcies);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestBorrowerData buildForTesting() {
      TestBorrowerData obj1 = new TestBorrowerData(action, id, incomeWorkflowData, borrowerType, firstName, middleName, lastName, suffix, alternativeNames, termsOfServiceConsentedAt, creditPullConsentedAt, emailAddress, phoneNumber, dateOfBirth, currentAddress, mailingAddress, maritalStatus, citizenshipType, spouseBorrowerId, isCurrentlyLivingWithCoBorrowers, isIntendingToOccupy, isPrimaryAuthorized, isSharingInformationWithCoBorrowers, militaryStatus, militaryServiceExpectedCompletionDate, employmentStatus, softCreditPullConsentType, creditPullStatus, creditPullType, creditScoreData, borrowerWorkflowData, homeownerPastThreeYears, outstandingJudgmentsIndicator, presentlyDelinquentIndicator, partyToLawsuitIndicator, priorPropertyDeedInLieuConveyedIndicator, priorPropertyShortSaleCompletedIndicator, priorPropertyForeclosureCompletedIndicator, bankruptcyIndicator, bankruptcies);
      return obj1;
    }
  }
}
