package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity
public class TestBankruptcyData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private TestBankruptcyChapterType bankruptcyChapterType;

  public TestBankruptcyData() {
    // JSON
  }

  public TestBankruptcyData(TestEntityAction action, String id,
      TestBankruptcyChapterType bankruptcyChapterType) {
    this.action = action;
    this.id = id;
    this.bankruptcyChapterType = bankruptcyChapterType;
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<TestBankruptcyChapterType> getBankruptcyChapterType() {
    return Option.of(bankruptcyChapterType);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.bankruptcyChapterType);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestBankruptcyData that = (TestBankruptcyData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(bankruptcyChapterType, that.bankruptcyChapterType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestBankruptcyData.class);
    if (isExactClass) {
      sb.append("TestBankruptcyData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  bankruptcyChapterType: ").append(bankruptcyChapterType).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withBankruptcyChapterType(getBankruptcyChapterType().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private TestBankruptcyChapterType bankruptcyChapterType = null;

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withBankruptcyChapterType(
        @Nullable TestBankruptcyChapterType bankruptcyChapterType) {
      this.bankruptcyChapterType = bankruptcyChapterType;
      return this;
    }

    public TestBankruptcyData build() {
      TestBankruptcyData obj1 = new TestBankruptcyData(action, id, bankruptcyChapterType);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestBankruptcyData buildForTesting() {
      TestBankruptcyData obj1 = new TestBankruptcyData(action, id, bankruptcyChapterType);
      return obj1;
    }
  }
}
