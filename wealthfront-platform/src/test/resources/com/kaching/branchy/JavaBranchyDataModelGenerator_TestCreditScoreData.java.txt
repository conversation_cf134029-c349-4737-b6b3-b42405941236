package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity
public class TestCreditScoreData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private String borrowerId;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditBureau bureau;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditScoreUnavailableReason unavailableReason;

  public TestCreditScoreData() {
    // JSON
  }

  public TestCreditScoreData(TestEntityAction action, String id, String borrowerId,
      TestCreditBureau bureau, TestCreditScoreUnavailableReason unavailableReason) {
    this.action = action;
    this.id = id;
    this.borrowerId = borrowerId;
    this.bureau = bureau;
    this.unavailableReason = unavailableReason;
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<String> getBorrowerId() {
    return Option.of(borrowerId);
  }

  public Option<TestCreditBureau> getBureau() {
    return Option.of(bureau);
  }

  public Option<TestCreditScoreUnavailableReason> getUnavailableReason() {
    return Option.of(unavailableReason);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.borrowerId, this.bureau, this.unavailableReason);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestCreditScoreData that = (TestCreditScoreData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(borrowerId, that.borrowerId) &&
        Objects.equals(bureau, that.bureau) &&
        Objects.equals(unavailableReason, that.unavailableReason);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestCreditScoreData.class);
    if (isExactClass) {
      sb.append("TestCreditScoreData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  borrowerId: ").append(borrowerId).append("\n");
    sb.append("  bureau: ").append(bureau).append("\n");
    sb.append("  unavailableReason: ").append(unavailableReason).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withBorrowerId(getBorrowerId().getOrNull())
      .withBureau(getBureau().getOrNull())
      .withUnavailableReason(getUnavailableReason().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private String borrowerId = null;

    @Nullable
    private TestCreditBureau bureau = null;

    @Nullable
    private TestCreditScoreUnavailableReason unavailableReason = null;

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withBorrowerId(@Nullable String borrowerId) {
      this.borrowerId = borrowerId;
      return this;
    }

    public Builder withBureau(@Nullable TestCreditBureau bureau) {
      this.bureau = bureau;
      return this;
    }

    public Builder withUnavailableReason(
        @Nullable TestCreditScoreUnavailableReason unavailableReason) {
      this.unavailableReason = unavailableReason;
      return this;
    }

    public TestCreditScoreData build() {
      TestCreditScoreData obj1 = new TestCreditScoreData(action, id, borrowerId, bureau, unavailableReason);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestCreditScoreData buildForTesting() {
      TestCreditScoreData obj1 = new TestCreditScoreData(action, id, borrowerId, bureau, unavailableReason);
      return obj1;
    }
  }
}
