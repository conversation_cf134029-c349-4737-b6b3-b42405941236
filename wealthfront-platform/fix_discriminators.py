#!/usr/bin/env python3

import re
import os

# Define the mapping of class names to their discriminator values
class_to_discriminator = {
    "TestBankruptcyData": "bankruptcy-data",
    "TestBorrowerData": "borrower-data", 
    "TestCreditPullData": "credit-pull-data",
    "TestCreditScoreData": "credit-score-data"
}

base_path = "src/test/resources/com/kaching/branchy"

for class_name, discriminator in class_to_discriminator.items():
    file_path = f"{base_path}/JavaBranchyDataModelGenerator_{class_name}.java.txt"
    
    if os.path.exists(file_path):
        print(f"Processing {class_name}")
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Replace @Entity with @Entity(discriminator = "...")
        pattern = r'@Entity\s+public class ' + class_name
        replacement = f'@Entity(discriminator = "{discriminator}")\npublic class {class_name}'
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(file_path, 'w') as f:
                f.write(new_content)
            print(f"Updated discriminator for {class_name}")
        else:
            print(f"Could not find @Entity pattern in {class_name}")
    else:
        print(f"File not found: {file_path}")

print("Done updating discriminators")
